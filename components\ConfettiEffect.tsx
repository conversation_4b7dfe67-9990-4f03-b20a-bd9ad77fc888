import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  runOnJS,
} from 'react-native-reanimated';

const { height } = Dimensions.get('window');

interface ConfettiPiece {
  id: number;
  x: number;
  y: number;
  color: string;
  size: number;
  rotation: number;
  velocityX: number;
  velocityY: number;
}

interface ConfettiEffectProps {
  id: string;
  x: number;
  y: number;
  onComplete: (id: string) => void;
}

const CONFETTI_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
  '#FFEAA7', '#DDA0DD', '#FFB347', '#FF69B4',
  '#A8E6CF', '#FFD93D', '#6BCF7F', '#4D9DE0'
];

export default function ConfettiEffect({ id, x, y, onComplete }: ConfettiEffectProps) {
  const confettiPieces: ConfettiPiece[] = [];
  
  // Créer 30 confettis
  for (let i = 0; i < 30; i++) {
    confettiPieces.push({
      id: i,
      x: 0,
      y: 0,
      color: CONFETTI_COLORS[Math.floor(Math.random() * CONFETTI_COLORS.length)],
      size: Math.random() * 8 + 4,
      rotation: Math.random() * 360,
      velocityX: (Math.random() - 0.5) * 300,
      velocityY: Math.random() * -200 - 100,
    });
  }

  useEffect(() => {
    // Nettoyer après l'animation
    const timer = setTimeout(() => {
      runOnJS(onComplete)(id);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={[styles.container, { left: x, top: y }]}>
      {confettiPieces.map((piece) => (
        <ConfettiPiece key={piece.id} piece={piece} />
      ))}
    </View>
  );
}

function ConfettiPiece({ piece }: { piece: ConfettiPiece }) {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotation = useSharedValue(piece.rotation);
  const opacity = useSharedValue(1);

  useEffect(() => {
    // Animation de dispersion avec physique réaliste
    translateX.value = withTiming(piece.velocityX, { duration: 2000 });
    translateY.value = withTiming(piece.velocityY + 500, { duration: 2000 }); // Gravité
    rotation.value = withTiming(rotation.value + 720, { duration: 2000 });
    
    // Fade out vers la fin
    opacity.value = withDelay(1500, withTiming(0, { duration: 500 }));
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotation.value}deg` },
    ],
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        styles.confetti,
        {
          backgroundColor: piece.color,
          width: piece.size,
          height: piece.size,
        },
        animatedStyle,
      ]}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    width: 1,
    height: 1,
  },
  confetti: {
    position: 'absolute',
    borderRadius: 2,
  },
});