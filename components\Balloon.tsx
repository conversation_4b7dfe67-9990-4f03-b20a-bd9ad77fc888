import React, { useEffect } from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

interface BalloonProps {
  id: string;
  x: number;
  y: number;
  color: string;
  size: number;
  onPop: (id: string, x: number, y: number) => void;
}

export default function Balloon({ id, x, y, color, size, onPop }: BalloonProps) {
  const scale = useSharedValue(0);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(1);

  // Couleur plus claire pour le dégradé
  const lighterColor = color + '80';

  useEffect(() => {
    // Animation d'apparition
    scale.value = withSpring(1, { damping: 8, stiffness: 100 });
    
    // Animation de flottement
    translateY.value = withRepeat(
      withTiming(-10, { duration: 2000 }),
      -1,
      true
    );
  }, []);

  const handlePress = () => {
    // Animation d'éclatement
    scale.value = withSequence(
      withTiming(1.3, { duration: 100 }),
      withTiming(0, { duration: 200 })
    );
    opacity.value = withTiming(0, { duration: 300 });
    
    // Déclencher l'effet de confettis après l'animation
    setTimeout(() => {
      runOnJS(onPop)(id, x + size / 2, y + size / 2);
    }, 200);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: x },
      { translateY: y + translateY.value },
      { scale: scale.value },
    ],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[styles.balloonContainer, animatedStyle]}>
      <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
        <LinearGradient
          colors={[lighterColor, color]}
          start={{ x: 0.3, y: 0.3 }}
          end={{ x: 0.7, y: 0.9 }}
          style={[
            styles.balloon,
            {
              width: size,
              height: size * 1.3,
              borderRadius: size / 2,
            },
          ]}
        />
        {/* Reflet du ballon */}
        <LinearGradient
          colors={['rgba(255,255,255,0.6)', 'transparent']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[
            styles.highlight,
            {
              width: size * 0.3,
              height: size * 0.4,
              borderRadius: size * 0.15,
              top: size * 0.1,
              left: size * 0.2,
            },
          ]}
        />
        {/* Ficelle du ballon */}
        <LinearGradient
          colors={['#8B4513', '#D2691E']}
          style={[
            styles.string,
            {
              left: size / 2 - 1,
              top: size * 1.3,
              height: size * 0.8,
            },
          ]}
        />
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  balloonContainer: {
    position: 'absolute',
  },
  balloon: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
  highlight: {
    position: 'absolute',
  },
  string: {
    position: 'absolute',
    width: 2,
    borderRadius: 1,
  },
});