import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Balloon from './Balloon';
import ConfettiEffect from './ConfettiEffect';

const { width, height } = Dimensions.get('window');

interface BalloonData {
  id: string;
  x: number;
  y: number;
  color: string;
  size: number;
}

interface BalloonScreenProps {
  screenId: string;
}

const BALLOON_COLORS = [
  '#FF6B6B', // Rouge
  '#4ECDC4', // Turquoise  
  '#45B7D1', // Bleu
  '#96CEB4', // Vert
  '#FFEAA7', // Jaune
  '#DDA0DD', // Violet
  '#FFB347', // Orange
  '#FF69B4', // Rose
];

export default function BalloonScreen({ screenId }: BalloonScreenProps) {
  const [balloons, setBalloons] = useState<BalloonData[]>([]);
  const [confetti, setConfetti] = useState<Array<{ id: string; x: number; y: number }>>([]);

  const generateBalloons = () => {
    const newBalloons: BalloonData[] = [];
    const safeArea = {
      minX: 60,
      maxX: width - 120,
      minY: 100,
      maxY: height - 200,
    };

    for (let i = 0; i < 8; i++) {
      let attempts = 0;
      let position;
      
      // Éviter les chevauchements
      do {
        position = {
          x: Math.random() * (safeArea.maxX - safeArea.minX) + safeArea.minX,
          y: Math.random() * (safeArea.maxY - safeArea.minY) + safeArea.minY,
        };
        attempts++;
      } while (
        attempts < 50 && 
        newBalloons.some(balloon => 
          Math.sqrt(Math.pow(balloon.x - position.x, 2) + Math.pow(balloon.y - position.y, 2)) < 100
        )
      );

      newBalloons.push({
        id: `${screenId}-balloon-${i}`,
        x: position.x,
        y: position.y,
        color: BALLOON_COLORS[i % BALLOON_COLORS.length],
        size: Math.random() * 20 + 50, // Taille entre 50 et 70
      });
    }

    setBalloons(newBalloons);
  };

  useEffect(() => {
    generateBalloons();
  }, [screenId]);

  const handleBalloonPop = (balloonId: string, x: number, y: number) => {
    // Supprimer le ballon
    setBalloons(prev => prev.filter(balloon => balloon.id !== balloonId));
    
    // Déclencher l'effet de confettis
    setConfetti(prev => [...prev, { id: `confetti-${Date.now()}`, x, y }]);
    
    // Régénérer un nouveau ballon après 2 secondes
    setTimeout(() => {
      const safeArea = {
        minX: 60,
        maxX: width - 120,
        minY: 100,
        maxY: height - 200,
      };
      
      const newBalloon: BalloonData = {
        id: `${screenId}-balloon-${Date.now()}`,
        x: Math.random() * (safeArea.maxX - safeArea.minX) + safeArea.minX,
        y: Math.random() * (safeArea.maxY - safeArea.minY) + safeArea.minY,
        color: BALLOON_COLORS[Math.floor(Math.random() * BALLOON_COLORS.length)],
        size: Math.random() * 20 + 50,
      };
      
      setBalloons(prev => [...prev, newBalloon]);
    }, 2000);
  };

  const handleConfettiComplete = (confettiId: string) => {
    setConfetti(prev => prev.filter(c => c.id !== confettiId));
  };

  return (
    <View style={styles.container}>
      {balloons.map((balloon) => (
        <Balloon
          key={balloon.id}
          id={balloon.id}
          x={balloon.x}
          y={balloon.y}
          color={balloon.color}
          size={balloon.size}
          onPop={handleBalloonPop}
        />
      ))}
      
      {confetti.map((conf) => (
        <ConfettiEffect
          key={conf.id}
          id={conf.id}
          x={conf.x}
          y={conf.y}
          onComplete={handleConfettiComplete}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
});